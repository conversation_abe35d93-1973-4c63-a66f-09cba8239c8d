package linetv

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kkapp/model/playback"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v3/manifest"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	"github.com/go-zoo/bone"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

const (
	bvTenantID = "test-tenant-id"
)

type LineTVHandlerSuite struct {
	suite.Suite
	ctrl                     *gomock.Controller
	mockManifestLegacyHelper *manifest.MockLegacyHelper
	mockPlaybackLegacyHelper *playback.MockLegacyHelper
	mockClock                *clock.MockClock

	handler Handler
	app     *bone.Mux
}

func TestLineTVHandlerSuite(t *testing.T) {
	suite.Run(t, new(LineTVHandlerSuite))
}

func (suite *LineTVHandlerSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockManifestLegacyHelper = manifest.NewMockLegacyHelper(suite.ctrl)
	suite.mockPlaybackLegacyHelper = playback.NewMockLegacyHelper(suite.ctrl)
	suite.mockClock = clock.NewMockClock(suite.ctrl)

	suite.handler = Handler{
		manifestLegacyHelper: suite.mockManifestLegacyHelper,
		playbackLegacyHelper: suite.mockPlaybackLegacyHelper,
		clock:                suite.mockClock,
	}

	config.KKSBVTenantIDForLineTV = bvTenantID
	suite.app = bone.New()
	suite.app.Post("/vendors/linetv/episodes/:episodeID/manifest", http.HandlerFunc(suite.handler.RequireStreamingManifest))
}

func (suite *LineTVHandlerSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func (suite *LineTVHandlerSuite) TestPostEpisodeManifest() {
	var (
		validEpisodeID   = "01000749010001"
		validTitleID     = "01000749"
		validUserID      = "ltv_test-user-id"
		validDeviceID    = "test-device-id"
		validTargetFile  = "p4"

		sampleMezzanineWithSubtitles = []manifest.Mezzanine{
			{
				DefaultSubtitle: "zh-Hant",
				SubtitleURL: map[string]string{
					"zh-Hant": "https://example.com/zh-Hant.vtt",
				},
				ThumbnailURL:     "https://example.com/thumbnail.vtt",
				SupportedQuality: []string{"1080p", "720p", "480p"},
				Dash: dbmeta.MezzanineFile{
					URL:  "https://example.com/dash.mpd",
					Size: 12345.67,
				},
				Hls: dbmeta.MezzanineFile{
					URL:  "https://example.com/hls.m3u8",
					Size: 23456.78,
				},
			},
		}

		samplePlayback = &playback.Playback{
			PlaybackToken: "test-playback-token",
		}
	)

	testcases := []struct {
		name           string
		episodeID      string
		deviceID       string
		requestBody    PostEpisodeManifestReq
		given          func()
		expectedStatus int
		assertBody     func(body []byte)
	}{
		{
			name:      "WHEN valid p4 target file request THEN return manifest and playback token",
			episodeID: validEpisodeID,
			deviceID:  validDeviceID,
			requestBody: PostEpisodeManifestReq{
				UserID:     validUserID,
				TargetFile: validTargetFile,
				Subtitles:  true,
				Purpose:    "playback",
			},
			given: func() {
				suite.mockGetManifestsByTargetFile([]string{validEpisodeID}, validTargetFile, "playback", true, sampleMezzanineWithSubtitles)
				suite.mockGetPlaybackToken(validUserID, validDeviceID, validTitleID, validEpisodeID, "playback", samplePlayback)
			},
			expectedStatus: http.StatusOK,
			assertBody: func(body []byte) {
				var response struct {
					Data PostEpisodeManifestResp `json:"data"`
				}
				suite.NoError(json.Unmarshal(body, &response))
				suite.Equal("zh-Hant", response.Data.StreamingAssets.DefaultSubtitle)
				suite.Equal("https://example.com/zh-Hant.vtt", response.Data.StreamingAssets.SubtitleURL["zh-Hant"])
				suite.Equal("https://example.com/thumbnail.vtt", response.Data.StreamingAssets.ThumbnailURL)
				suite.Equal([]string{"1080p", "720p", "480p"}, response.Data.StreamingAssets.SupportedQuality)
				suite.Equal("https://example.com/dash.mpd", response.Data.StreamingAssets.Dash.URL)
				suite.Equal(12345.67, response.Data.StreamingAssets.Dash.Size)
				suite.Equal("https://example.com/hls.m3u8", response.Data.StreamingAssets.Hls.URL)
				suite.Equal(23456.78, response.Data.StreamingAssets.Hls.Size)
				suite.Equal("test-playback-token", response.Data.PlaybackToken.Token)
				suite.NotEmpty(response.Data.PlaybackToken.CreatedAt)
				suite.NotEmpty(response.Data.PlaybackToken.ExpiresAt)
				suite.NotEmpty(response.Data.LicenseURL)
				suite.Contains(response.Data.LicenseURL, "fairplay_cert")
				suite.NotEmpty(response.Data.LicenseHeaders)
				suite.Equal("X-KK-Tenant-Id", response.Data.LicenseHeaders[0].Key)
				suite.Equal(bvTenantID, response.Data.LicenseHeaders[0].Value)
				suite.ElementsMatch([]string{"playready", "widevine"}, response.Data.LicenseHeaders[0].For)
			},
		},
		{
			name:      "WHEN invalid target file p6 THEN return 400 error",
			episodeID: validEpisodeID,
			deviceID:  validDeviceID,
			requestBody: PostEpisodeManifestReq{
				UserID:     validUserID,
				TargetFile: "p6",
				Subtitles:  false,
				Purpose:    "playback",
			},
			given:          func() {},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:      "WHEN missing device ID THEN return 400 error",
			episodeID: validEpisodeID,
			deviceID:  "",
			requestBody: PostEpisodeManifestReq{
				UserID:     validUserID,
				TargetFile: validTargetFile,
				Subtitles:  false,
				Purpose:    "playback",
			},
			given:          func() {},
			expectedStatus: http.StatusBadRequest,
			assertBody: func(body []byte) {
				var response struct {
					Error rest.Err `json:"error"`
				}
				suite.NoError(json.Unmarshal(body, &response))
				suite.Equal("400.1", response.Error.Code)
				suite.Equal("missing device ID", response.Error.Message)
			},
		},
		{
			name:      "WHEN invalid episode ID THEN return 400 error",
			episodeID: "invalid",
			deviceID:  validDeviceID,
			requestBody: PostEpisodeManifestReq{
				UserID:     validUserID,
				TargetFile: validTargetFile,
				Subtitles:  false,
				Purpose:    "playback",
			},
			given:          func() {},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:      "WHEN subtitles false THEN return manifest without subtitles",
			episodeID: validEpisodeID,
			deviceID:  validDeviceID,
			requestBody: PostEpisodeManifestReq{
				UserID:     validUserID,
				TargetFile: validTargetFile,
				Subtitles:  false,
				Purpose:    "playback",
			},
			given: func() {
				mezzaninesWithoutSubtitles := []manifest.Mezzanine{
					{
						ThumbnailURL:     "https://example.com/thumbnail.vtt",
						SupportedQuality: []string{"720p", "480p"},
						Dash: dbmeta.MezzanineFile{
							URL:  "https://example.com/dash.mpd",
							Size: 12345.67,
						},
						Hls: dbmeta.MezzanineFile{
							URL:  "https://example.com/hls.m3u8",
							Size: 23456.78,
						},
					},
				}
				suite.mockGetManifestsByTargetFile([]string{validEpisodeID}, validTargetFile, "playback", false, mezzaninesWithoutSubtitles)
				suite.mockGetPlaybackToken(validUserID, validDeviceID, validTitleID, validEpisodeID, "playback", samplePlayback)
			},
			expectedStatus: http.StatusOK,
			assertBody: func(body []byte) {
				var response struct {
					Data PostEpisodeManifestResp `json:"data"`
				}
				suite.NoError(json.Unmarshal(body, &response))
				suite.Empty(response.Data.StreamingAssets.DefaultSubtitle)
				suite.Empty(response.Data.StreamingAssets.SubtitleURL)
				suite.Equal("https://example.com/thumbnail.vtt", response.Data.StreamingAssets.ThumbnailURL)
				suite.Equal([]string{"720p", "480p"}, response.Data.StreamingAssets.SupportedQuality)
				suite.Equal("test-playback-token", response.Data.PlaybackToken.Token)
				suite.NotEmpty(response.Data.LicenseHeaders)
			},
		},
		{
			name:      "WHEN invalid purpose THEN return 400 error",
			episodeID: validEpisodeID,
			deviceID:  validDeviceID,
			requestBody: PostEpisodeManifestReq{
				UserID:     validUserID,
				TargetFile: validTargetFile,
				Subtitles:  false,
				Purpose:    "invalid_purpose",
			},
			given:          func() {},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:      "WHEN episode not found THEN return 404 error",
			episodeID: validEpisodeID,
			deviceID:  validDeviceID,
			requestBody: PostEpisodeManifestReq{
				UserID:     validUserID,
				TargetFile: validTargetFile,
				Subtitles:  false,
				Purpose:    "playback",
			},
			given: func() {
				suite.mockGetManifestsByTargetFileNotFound([]string{validEpisodeID}, validTargetFile, "playback", false)
			},
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			requestBody, _ := json.Marshal(tc.requestBody)
			req := httptest.NewRequest(http.MethodPost, "/vendors/linetv/episodes/"+tc.episodeID+"/manifest", bytes.NewReader(requestBody))
			req.Header.Set("Content-Type", "application/json")
			if tc.deviceID != "" {
				req.Header.Set(httpreq.HeaderDeviceID, tc.deviceID)
			}

			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			suite.Equal(tc.expectedStatus, rr.Code)
			if tc.assertBody != nil {
				tc.assertBody(rr.Body.Bytes())
			}
		})
	}
}

func (suite *LineTVHandlerSuite) mockGetManifestsByTargetFile(episodeIDs []string, targetFile string, purpose string, subtitles bool, mezzanines []manifest.Mezzanine) {
	suite.mockManifestLegacyHelper.EXPECT().
		GetManifestsByTargetFile(episodeIDs, targetFile, purpose, subtitles).
		Return(mezzanines, nil)
}

func (suite *LineTVHandlerSuite) mockGetManifestsByTargetFileNotFound(episodeIDs []string, targetFile string, purpose string, subtitles bool) {
	suite.mockManifestLegacyHelper.EXPECT().
		GetManifestsByTargetFile(episodeIDs, targetFile, purpose, subtitles).
		Return([]manifest.Mezzanine{}, nil)
}

func (suite *LineTVHandlerSuite) mockGetPlaybackToken(userID, deviceID, titleID, episodeID, purpose string, playbackResult *playback.Playback) {
	suite.mockPlaybackLegacyHelper.EXPECT().
		GetToken(userID, deviceID, playback.PlaybackRequest{
			TitleID:   titleID,
			EpisodeID: episodeID,
			Medium:    "SVOD",
			Purpose:   purpose,
		}).
		Return(playbackResult, nil)
}
